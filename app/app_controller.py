"""
Application Controller
======================

Main application controller coordinating between views and services.
"""

import flet as ft
import logging
import asyncio
import time
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path
from datetime import datetime

# State Management
from app.app_state import AppState
from models.ui_state import UIState, TabState
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions

# Services
from services.financial_service import FinancialModelService
from services.validation_service import ValidationService
from services.export_service import ExportService
from services.location_service import LocationComparisonService
from services.report_service import ReportGenerationService
from services.ai_analysis_service import AIAnalysisService, LLMConfig

# Views
from views.project_setup_view import ProjectSetupView
from views.dashboard_view import DashboardView
from views.location_comparison_view import LocationComparisonView
from views.financial_model_view import FinancialModelView
from views.validation_view import ValidationView
from views.ai_analysis_view import AIAnalysisView
from views.ai_settings_view import AISettingsView

# UI Components
# from components.ui.modern_navigation import ModernSidebar, ModernTopBar


class AppController:
    """Main application controller coordinating between views and services."""
    
    def __init__(self, page: ft.Page):
        """Initialize the AppController with the given page."""
        self.page = page
        self.logger = logging.getLogger(__name__)
        
        # Initialize state management
        self.app_state = AppState()
        self.ui_state = UIState()
        
        # Initialize services
        self._initialize_services()
        
        # Initialize UI components
        self._initialize_ui_components()
        
        # Initialize views
        self._initialize_views()
        
        # Setup page layout
        self._setup_page_layout()
        
        # Initial navigation
        self.navigate_to_tab(TabState.PROJECT_SETUP)
    
    def _initialize_services(self):
        """Initialize all required services."""
        try:
            self.financial_service = FinancialModelService()
            self.validation_service = ValidationService()
            self.export_service = ExportService()
            self.location_service = LocationComparisonService()
            self.report_service = ReportGenerationService()

            # Initialize AI service with default configuration
            self.ai_service = None  # Will be initialized when settings are loaded
            self._load_ai_settings()

            self.logger.info("Services initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize services: {e}")
            self.show_error("Failed to initialize application services")
    
    def _initialize_ui_components(self):
        """Initialize UI components."""
        try:
            # Create simple sidebar for now
            self.sidebar = self._create_simple_sidebar()
            
            # Create simple topbar
            self.topbar = self._create_simple_topbar()
            
            self.logger.info("UI components initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize UI components: {e}")
            self.show_error("Failed to initialize UI components")
    
    def _create_simple_sidebar(self):
        """Create a simple sidebar navigation."""
        navigation_items = [
            ft.NavigationRailDestination(
                icon=ft.Icons.SETTINGS_OUTLINED,
                selected_icon=ft.Icons.SETTINGS,
                label="Project Setup"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.DASHBOARD_OUTLINED,
                selected_icon=ft.Icons.DASHBOARD,
                label="Dashboard"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.MAP_OUTLINED,
                selected_icon=ft.Icons.MAP,
                label="Location Comparison"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.CALCULATE_OUTLINED,
                selected_icon=ft.Icons.CALCULATE,
                label="Financial Model"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.VERIFIED_OUTLINED,
                selected_icon=ft.Icons.VERIFIED,
                label="Validation"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.SMART_TOY_OUTLINED,
                selected_icon=ft.Icons.SMART_TOY,
                label="AI Analysis"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.TUNE_OUTLINED,
                selected_icon=ft.Icons.TUNE,
                label="AI Settings"
            )
        ]
        
        return ft.NavigationRail(
            destinations=navigation_items,
            on_change=self._handle_navigation,
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=200,
            min_extended_width=250
        )
    
    def _create_simple_topbar(self):
        """Create a simple topbar."""
        return ft.AppBar(
            title=ft.Text("Hiel RnE Modeler v3.0"),
            actions=[
                ft.IconButton(
                    icon=ft.Icons.DOWNLOAD,
                    tooltip="Export",
                    on_click=lambda e: self._handle_export("excel")
                ),
                ft.IconButton(
                    icon=ft.Icons.SETTINGS,
                    tooltip="Settings",
                    on_click=lambda e: self._handle_settings()
                )
            ]
        )
    
    def _initialize_views(self):
        """Initialize all views."""
        try:
            self.views = {
                TabState.PROJECT_SETUP: ProjectSetupView(self.page),
                TabState.DASHBOARD: DashboardView(self.page),
                TabState.LOCATION_COMPARISON: LocationComparisonView(self.page),
                TabState.FINANCIAL_MODEL: FinancialModelView(self.page),
                TabState.VALIDATION: ValidationView(self.page),
                TabState.AI_ANALYSIS: AIAnalysisView(self.page),
                TabState.AI_SETTINGS: AISettingsView(self.page)
            }

            # Connect view callbacks to controller methods and set app controller reference
            for view in self.views.values():
                view.on_action_requested = self.handle_action_request
                view.on_data_changed = self.handle_data_change
                # Set reference to app controller so views can access AI service
                if hasattr(view, 'page'):
                    view.page.app_controller = self

            self.logger.info("Views initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize views: {e}")
            self.show_error("Failed to initialize application views")
    
    def _setup_page_layout(self):
        """Setup the main page layout."""
        try:
            # Set the appbar on the page
            self.page.appbar = self.topbar

            # Create main container without the topbar (since it's now in page.appbar)
            self.main_container = ft.Container(
                content=ft.Row([
                    # Sidebar
                    self.sidebar,

                    # Content area
                    ft.Container(
                        content=ft.Column([
                            # Content will be updated by navigate_to_tab
                        ]),
                        expand=True,
                        padding=20
                    )
                ], expand=True),
                expand=True
            )

            self.page.add(self.main_container)
            self.page.update()
            self.logger.info("Page layout setup completed")
        except Exception as e:
            self.logger.error(f"Failed to setup page layout: {e}")
            self.show_error("Failed to setup application layout")
    
    def navigate_to_tab(self, tab_state: TabState):
        """Navigate to the specified tab."""
        try:
            self.ui_state.current_tab = tab_state
            
            # Update sidebar selection
            tab_index_map = {
                TabState.PROJECT_SETUP: 0,
                TabState.DASHBOARD: 1,
                TabState.LOCATION_COMPARISON: 2,
                TabState.FINANCIAL_MODEL: 3,
                TabState.VALIDATION: 4,
                TabState.AI_ANALYSIS: 5,
                TabState.AI_SETTINGS: 6
            }
            
            if tab_state in tab_index_map:
                self.sidebar.selected_index = tab_index_map[tab_state]
                self.sidebar.update()
            
            # Get the view for this tab
            if tab_state in self.views:
                view = self.views[tab_state]

                # Update content area (adjusted for new layout structure)
                content_container = self.main_container.content.controls[1]
                content_container.content = view.get_content()
                content_container.update()
            
            self.logger.info(f"Navigated to tab: {tab_state}")
        except Exception as e:
            self.logger.error(f"Failed to navigate to tab {tab_state}: {e}")
            self.show_error(f"Failed to navigate to {tab_state}")
    
    def _handle_navigation(self, e):
        """Handle navigation requests from UI components."""
        tab_map = {
            0: TabState.PROJECT_SETUP,
            1: TabState.DASHBOARD,
            2: TabState.LOCATION_COMPARISON,
            3: TabState.FINANCIAL_MODEL,
            4: TabState.VALIDATION,
            5: TabState.AI_ANALYSIS,
            6: TabState.AI_SETTINGS
        }
        
        if e.control.selected_index in tab_map:
            self.navigate_to_tab(tab_map[e.control.selected_index])
    
    def _handle_export(self, export_type: str):
        """Handle export requests."""
        try:
            # Implement export logic
            self.show_success(f"Export {export_type} started")
        except Exception as e:
            self.logger.error(f"Failed to handle export: {e}")
            self.show_error("Export failed")
    
    def _handle_settings(self):
        """Handle settings requests."""
        try:
            # Implement settings logic
            self.show_success("Settings opened")
        except Exception as e:
            self.logger.error(f"Failed to handle settings: {e}")
            self.show_error("Failed to open settings")
    
    def handle_data_change(self, data_type: str, data: Any):
        """Handle data changes from views."""
        try:
            if data_type == "client_profile":
                self.app_state.client_profile = data
            elif data_type == "project_assumptions":
                self.app_state.project_assumptions = data
            
            self.logger.info(f"Data updated: {data_type}")
        except Exception as e:
            self.logger.error(f"Failed to handle data change: {e}")
            self.show_error("Failed to update data")
    
    def handle_action_request(self, action: str, params: Dict[str, Any] = None):
        """Handle action requests from views."""
        try:
            if action == "run_financial_model":
                self._run_financial_model()
            elif action == "run_validation":
                self._run_validation()
            elif action == "run_location_comparison":
                self._run_location_comparison()
            elif action == "run_ai_analysis":
                self._run_ai_analysis(params)
            elif action == "ai_chat_message":
                self._handle_ai_chat_message(params)
            elif action == "ai_settings_updated":
                self._handle_ai_settings_updated(params)

            self.logger.info(f"Action handled: {action}")
        except Exception as e:
            self.logger.error(f"Failed to handle action {action}: {e}")
            self.show_error(f"Failed to execute {action}")
    
    def _run_financial_model(self):
        """Run the financial model."""
        try:
            # Run financial model
            results = self.financial_service.calculate_financial_model(
                self.app_state.client_profile,
                self.app_state.project_assumptions
            )
            
            self.app_state.financial_results = results
            self.show_success("Financial model calculated successfully")
        except Exception as e:
            self.logger.error(f"Failed to run financial model: {e}")
            self.show_error("Failed to run financial model")
    
    def _run_validation(self):
        """Run model validation."""
        try:
            # Run validation
            results = self.validation_service.validate_model(
                self.app_state.financial_results
            )
            
            self.app_state.validation_results = results
            self.show_success("Model validation completed")
        except Exception as e:
            self.logger.error(f"Failed to run validation: {e}")
            self.show_error("Failed to run validation")
    
    def _run_location_comparison(self):
        """Run location comparison."""
        try:
            # Run location comparison
            results = self.location_service.compare_locations(
                self.app_state.project_assumptions
            )

            self.app_state.location_results = results
            self.show_success("Location comparison completed")
        except Exception as e:
            self.logger.error(f"Failed to run location comparison: {e}")
            self.show_error("Failed to run location comparison")

    def _run_ai_analysis(self, params: Dict[str, Any] = None):
        """Run AI analysis."""
        try:
            if not self.ai_service:
                self.show_error("AI service not configured. Please check AI settings.")
                return

            if not self.app_state.client_profile or not self.app_state.project_assumptions:
                self.show_error("Please complete project setup before running AI analysis.")
                return

            # Show loading message
            self.show_success("Starting AI analysis...")

            # Run AI analysis in background
            import threading
            def run_analysis():
                try:
                    # Create analysis data
                    analysis_data = {
                        'client_profile': self.app_state.client_profile.to_dict() if hasattr(self.app_state.client_profile, 'to_dict') else self.app_state.client_profile.__dict__,
                        'project_assumptions': self.app_state.project_assumptions.to_dict() if hasattr(self.app_state.project_assumptions, 'to_dict') else self.app_state.project_assumptions.__dict__,
                        'financial_results': self.app_state.financial_results.__dict__ if self.app_state.financial_results else {}
                    }

                    # Run analysis
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    result = loop.run_until_complete(
                        self.ai_service.analyze_financial_data(
                            financial_results=analysis_data['financial_results'],
                            client_profile=analysis_data['client_profile'],
                            assumptions=analysis_data['project_assumptions'],
                            analysis_type=params.get('analysis_type', 'comprehensive') if params else 'comprehensive'
                        )
                    )

                    # Update AI Analysis view with results
                    if TabState.AI_ANALYSIS in self.views:
                        ai_view = self.views[TabState.AI_ANALYSIS]
                        if hasattr(ai_view, 'set_analysis_results'):
                            # Format results for display
                            formatted_results = {
                                'executive_summary': result.executive_summary if hasattr(result, 'executive_summary') else "Analysis completed successfully.",
                                'insights': result.insights if hasattr(result, 'insights') else {},
                                'recommendations': result.recommendations if hasattr(result, 'recommendations') else [],
                                'narrative': result.narrative if hasattr(result, 'narrative') else "AI analysis has been completed. You can now ask questions about your project."
                            }
                            ai_view.set_analysis_results(formatted_results)
                        elif hasattr(ai_view, 'set_initial_analysis'):
                            # Fallback to initial analysis method
                            summary = result.narrative if hasattr(result, 'narrative') else "AI analysis completed successfully. You can now ask questions about your project."
                            ai_view.set_initial_analysis(summary)

                    # Update Dashboard with AI insights
                    if TabState.DASHBOARD in self.views:
                        dashboard_view = self.views[TabState.DASHBOARD]

                        # Store AI analysis results in app state
                        self.app_state.ai_analysis_results = result

                        # Create AI insights for dashboard
                        ai_insights = {
                            'analysis_summary': result.narrative if hasattr(result, 'narrative') else "AI analysis completed",
                            'key_insights': result.insights if hasattr(result, 'insights') else {},
                            'recommendations': result.recommendations if hasattr(result, 'recommendations') else [],
                            'confidence_score': 0.85,  # Default confidence
                            'analysis_timestamp': datetime.now().isoformat()
                        }

                        # Update dashboard with AI insights
                        if hasattr(dashboard_view, 'set_ai_insights'):
                            dashboard_view.set_ai_insights(ai_insights)
                        elif hasattr(dashboard_view, 'update_data'):
                            dashboard_view.update_data({'ai_insights': ai_insights})

                        # Force refresh dashboard
                        if hasattr(dashboard_view, 'refresh'):
                            def refresh_dashboard_ui():
                                dashboard_view.refresh()
                                dashboard_view.page.update()
                            self.page.run_thread(refresh_dashboard_ui)

                    # Show success message
                    def show_success_ui():
                        self.show_success("AI analysis completed successfully! Check the Dashboard for insights.")

                    # Schedule UI update on main thread
                    self.page.run_thread(show_success_ui)

                except Exception as e:
                    self.logger.error(f"AI analysis failed: {e}")
                    def show_error_ui():
                        self.show_error(f"AI analysis failed: {str(e)}")
                    self.page.run_thread(show_error_ui)
                finally:
                    loop.close()

            # Start analysis in background thread
            analysis_thread = threading.Thread(target=run_analysis)
            analysis_thread.daemon = True
            analysis_thread.start()

        except Exception as e:
            self.logger.error(f"Failed to start AI analysis: {e}")
            self.show_error("Failed to start AI analysis")

    def _handle_ai_chat_message(self, params: Dict[str, Any] = None):
        """Handle AI chat message."""
        try:
            if not self.ai_service:
                self.show_error("AI service not configured. Please check AI settings.")
                return

            if not params or 'message' not in params:
                self.show_error("Invalid chat message parameters.")
                return

            message = params['message']
            chat_history = params.get('chat_history', [])

            # Run chat response in background
            import threading
            def run_chat():
                try:
                    # Create context for chat
                    context_data = {
                        'client_profile': self.app_state.client_profile.to_dict() if self.app_state.client_profile and hasattr(self.app_state.client_profile, 'to_dict') else {},
                        'project_assumptions': self.app_state.project_assumptions.to_dict() if self.app_state.project_assumptions and hasattr(self.app_state.project_assumptions, 'to_dict') else {},
                        'financial_results': self.app_state.financial_results.__dict__ if self.app_state.financial_results else {}
                    }

                    # Create a simple chat prompt
                    chat_prompt = f"""
                    Based on the renewable energy project context, please respond to this user question: {message}

                    Project Context:
                    - Client: {context_data.get('client_profile', {}).get('company_name', 'Unknown')}
                    - Technology: {context_data.get('project_assumptions', {}).get('technology_type', 'Unknown')}
                    - Capacity: {context_data.get('project_assumptions', {}).get('capacity_mw', 'Unknown')} MW
                    - Location: {context_data.get('project_assumptions', {}).get('project_location', 'Unknown')}

                    Please provide a helpful, professional response related to renewable energy project analysis.
                    """

                    # Use the AI service to generate response
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # Create a simple analysis request for chat
                    from services.ai_analysis_service import AnalysisRequest
                    request = AnalysisRequest(
                        analysis_type="chat",
                        data={'message': message, 'context': context_data},
                        context={'chat_history': chat_history},
                        output_format="narrative",
                        include_charts=False,
                        include_recommendations=False
                    )

                    result = loop.run_until_complete(self.ai_service.provider.generate_analysis(request))
                    response = result.narrative if hasattr(result, 'narrative') else "I'm here to help with your renewable energy project analysis. Please ask me specific questions about your project."

                    # Update AI Analysis view with response
                    if TabState.AI_ANALYSIS in self.views:
                        ai_view = self.views[TabState.AI_ANALYSIS]
                        if hasattr(ai_view, 'add_ai_response'):
                            def update_chat_ui():
                                ai_view.add_ai_response(response)
                            self.page.run_thread(update_chat_ui)

                except Exception as e:
                    self.logger.error(f"AI chat failed: {e}")
                    # Send error response
                    if TabState.AI_ANALYSIS in self.views:
                        ai_view = self.views[TabState.AI_ANALYSIS]
                        if hasattr(ai_view, 'add_ai_response'):
                            def update_chat_error_ui():
                                ai_view.add_ai_response("I apologize, but I'm having trouble processing your request right now. Please try again later.")
                            self.page.run_thread(update_chat_error_ui)
                finally:
                    loop.close()

            # Start chat response in background thread
            chat_thread = threading.Thread(target=run_chat)
            chat_thread.daemon = True
            chat_thread.start()

        except Exception as e:
            self.logger.error(f"Failed to handle AI chat message: {e}")
            self.show_error("Failed to process chat message")
    
    def show_success(self, message: str):
        """Show success message."""
        try:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.Colors.GREEN_400
            )
            self.page.overlay.append(snack_bar)
            snack_bar.open = True
            self.page.update()
        except Exception as e:
            self.logger.error(f"Failed to show success message: {e}")
    
    def show_error(self, message: str):
        """Show error message."""
        try:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.Colors.RED_400
            )
            self.page.overlay.append(snack_bar)
            snack_bar.open = True
            self.page.update()
        except Exception as e:
            self.logger.error(f"Failed to show error message: {e}")
    
    def show_warning(self, message: str):
        """Show warning message."""
        try:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.Colors.ORANGE_400
            )
            self.page.overlay.append(snack_bar)
            snack_bar.open = True
            self.page.update()
        except Exception as e:
            self.logger.error(f"Failed to show warning message: {e}")

    def _load_ai_settings(self):
        """Load AI settings and initialize AI service."""
        try:
            from pathlib import Path
            import json

            settings_path = Path('config/ai_settings.json')
            if settings_path.exists():
                with open(settings_path, 'r') as f:
                    settings = json.load(f)

                ai_config = settings.get('ai_config', {})
                if ai_config:
                    self._initialize_ai_service(ai_config)
            else:
                # Use default configuration
                default_config = {
                    'provider': 'openrouter',  # Will be mapped to 'custom' in _initialize_ai_service
                    'api_url': 'https://openrouter.ai/api/v1/chat/completions',
                    'api_key': '',
                    'model_id': 'moonshotai/kimi-k2:free',
                    'system_prompt': 'You are a professional financial analyst specializing in renewable energy projects.',
                    'max_tokens': 4000,
                    'temperature': 0.3,
                    'timeout': 60,
                    'rate_limit': 10,
                    'privacy_mode': True,
                    'enable_caching': True
                }
                self._initialize_ai_service(default_config)

        except Exception as e:
            self.logger.error(f"Failed to load AI settings: {e}")

    def _initialize_ai_service(self, ai_config: Dict[str, Any]):
        """Initialize AI service with given configuration."""
        try:
            # Map provider names to AI service provider names
            provider = ai_config.get('provider', 'openrouter')
            if provider == 'openrouter':
                provider = 'custom'  # OpenRouter uses custom provider in AI service

            # Convert config dict to LLMConfig
            llm_config = LLMConfig(
                provider=provider,
                api_url=ai_config.get('api_url', 'https://openrouter.ai/api/v1/chat/completions'),
                api_key=ai_config.get('api_key', ''),
                model_id=ai_config.get('model_id', 'moonshotai/kimi-k2:free'),
                system_prompt=ai_config.get('system_prompt', 'You are a professional financial analyst.'),
                max_tokens=ai_config.get('max_tokens', 4000),
                temperature=ai_config.get('temperature', 0.3),
                timeout=ai_config.get('timeout', 60),
                rate_limit=ai_config.get('rate_limit', 10),
                enable_caching=ai_config.get('enable_caching', True),
                privacy_mode=ai_config.get('privacy_mode', True)
            )

            # Initialize AI service
            self.ai_service = AIAnalysisService(llm_config)
            self.logger.info(f"AI service initialized with provider: {llm_config.provider}")

        except Exception as e:
            self.logger.error(f"Failed to initialize AI service: {e}")
            self.ai_service = None

    def _handle_ai_settings_updated(self, settings: Dict[str, Any]):
        """Handle AI settings update from AI Settings view."""
        try:
            if settings and 'ai_config' in settings:
                ai_config = settings['ai_config']

                # Update AI service configuration
                self._initialize_ai_service(ai_config)

                # Update AI Analysis view if it exists
                if TabState.AI_ANALYSIS in self.views:
                    ai_analysis_view = self.views[TabState.AI_ANALYSIS]
                    if hasattr(ai_analysis_view, 'update_ai_service'):
                        ai_analysis_view.update_ai_service(self.ai_service)

                self.show_success("AI settings updated successfully")
                self.logger.info("AI settings updated and applied")

        except Exception as e:
            self.logger.error(f"Failed to handle AI settings update: {e}")
            self.show_error("Failed to update AI settings")

    def get_ai_service(self):
        """Get the current AI service instance."""
        return self.ai_service