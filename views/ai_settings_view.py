"""
AI Settings Configuration View
==============================

Professional settings screen for configuring AI analysis providers,
security settings, and advanced features.
"""

import flet as ft
from typing import Dict, Any, Optional, List
import json
from pathlib import Path
from datetime import datetime
import asyncio

from .base_view import BaseView


class AISettingsView(BaseView):
    """View for AI settings configuration."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        
        # AI Configuration - 2025 Modern Setup
        import os
        
        # Try to load API key from environment variable first
        default_api_key = os.environ.get('OPENROUTER_API_KEY', '')
        if not default_api_key:
            # If no environment variable, check for a local config file
            try:
                config_path = Path.home() / '.hiel_rne_config' / 'api_keys.json'
                if config_path.exists():
                    with open(config_path, 'r') as f:
                        keys = json.load(f)
                        default_api_key = keys.get('openrouter_api_key', '')
            except:
                default_api_key = ''
        
        self.ai_config = {
            'provider': 'custom',  # Use custom provider for OpenRouter
            'api_url': 'https://openrouter.ai/api/v1/chat/completions',
            'api_key': '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',
            'model_id': 'anthropic/claude-3.5-sonnet',
            'system_prompt': self._get_default_system_prompt(),
            'max_tokens': 4000,
            'temperature': 0.3,
            'timeout': 60,
            'rate_limit': 10,
            'privacy_mode': True,
            'enable_caching': True,
            'enable_streaming': False,
            'custom_headers': {}
        }
        
        # Security Settings
        self.security_config = {
            'enable_encryption': True,
            'enable_data_anonymization': True,
            'enable_audit_logging': True,
            'data_retention_policy': 'no_retention',
            'access_control_enabled': False,
            'allowed_domains': [],
            'blocked_domains': []
        }
        
        # Performance Settings
        self.performance_config = {
            'enable_parallel_processing': True,
            'max_workers': 4,
            'enable_ai_cache': True,
            'cache_ttl_minutes': 60,
            'batch_processing': False,
            'optimize_for_speed': True
        }
        
        # UI Components
        self.provider_dropdown = None
        self.api_key_field = None
        self.model_id_field = None
        self.system_prompt_field = None
        self.test_connection_button = None
        self.connection_status = None
        self.privacy_mode_switch = None
        self.data_retention_dropdown = None
        self.encryption_switch = None
        self.audit_logging_switch = None
        
        # Test connection status
        self.connection_tested = False
        self.connection_result = None
        
        # Load saved settings
        self._load_settings()
    
    def build_content(self) -> ft.Control:
        """Build the AI settings view content."""
        
        # Header
        header = self.create_section_header(
            "AI Analysis Settings",
            "Configure AI providers, security, and performance settings"
        )
        
        # Settings tabs
        tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="AI Provider",
                    icon=ft.Icons.SMART_TOY,
                    content=self._build_ai_provider_tab()
                ),
                ft.Tab(
                    text="Security & Privacy",
                    icon=ft.Icons.SECURITY,
                    content=self._build_security_tab()
                ),
                ft.Tab(
                    text="Performance",
                    icon=ft.Icons.SPEED,
                    content=self._build_performance_tab()
                ),
                ft.Tab(
                    text="Advanced",
                    icon=ft.Icons.SETTINGS,
                    content=self._build_advanced_tab()
                )
            ]
        )
        
        # Action buttons
        action_buttons = ft.Row([
            self.create_action_button(
                "Test Connection",
                ft.Icons.WIFI_TETHERING,
                self._test_ai_connection,
                ft.Colors.BLUE_600
            ),
            self.create_action_button(
                "Save Settings",
                ft.Icons.SAVE,
                self._save_settings,
                ft.Colors.GREEN_600
            ),
            self.create_action_button(
                "Reset to Defaults",
                ft.Icons.RESTORE,
                self._reset_to_defaults,
                ft.Colors.ORANGE_600
            ),
            self.create_action_button(
                "Export Config",
                ft.Icons.DOWNLOAD,
                self._export_config,
                ft.Colors.PURPLE_600
            )
        ], alignment=ft.MainAxisAlignment.CENTER)
        
        # Connection status
        self.connection_status = ft.Container(
            content=ft.Text("Connection not tested", color=ft.Colors.GREY_600),
            padding=ft.padding.all(10),
            bgcolor=ft.Colors.GREY_100,
            border_radius=8,
            visible=False
        )
        
        return ft.Column([
            header,
            tabs,
            ft.Divider(height=20),
            action_buttons,
            self.connection_status
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _build_ai_provider_tab(self) -> ft.Container:
        """Build AI provider configuration tab."""
        
        # Provider selection
        self.provider_dropdown = ft.Dropdown(
            label="AI Provider",
            value=self.ai_config['provider'],
            options=[
                ft.dropdown.Option("custom", "OpenRouter (Multi-Model Gateway) [CONFIGURED]"),
                ft.dropdown.Option("openai", "OpenAI (GPT-4, GPT-4o, GPT-3.5)"),
                ft.dropdown.Option("anthropic", "Anthropic (Claude 3.5 Sonnet, Claude 3 Opus)"),
                ft.dropdown.Option("google", "Google (Gemini Pro, Gemini Ultra)"),
                ft.dropdown.Option("local", "Local Model (Ollama, LM Studio)")
            ],
            width=350,
            on_change=self._on_provider_change
        )
        
        # API Configuration
        api_url_field = ft.TextField(
            label="API URL",
            value=self.ai_config['api_url'],
            width=400,
            on_change=lambda e: self._update_config('api_url', e.control.value)
        )
        
        self.api_key_field = ft.TextField(
            label="API Key",
            value=self.ai_config['api_key'],
            password=True,
            width=400,
            on_change=lambda e: self._update_config('api_key', e.control.value)
        )
        
        self.model_id_field = ft.TextField(
            label="Model ID",
            value=self.ai_config['model_id'],
            width=300,
            on_change=lambda e: self._update_config('model_id', e.control.value)
        )
        
        # Advanced parameters
        max_tokens_field = ft.TextField(
            label="Max Tokens",
            value=str(self.ai_config['max_tokens']),
            width=150,
            on_change=lambda e: self._update_config('max_tokens', int(e.control.value or 4000))
        )
        
        temperature_field = ft.TextField(
            label="Temperature",
            value=str(self.ai_config['temperature']),
            width=150,
            on_change=lambda e: self._update_config('temperature', float(e.control.value or 0.3))
        )
        
        timeout_field = ft.TextField(
            label="Timeout (seconds)",
            value=str(self.ai_config['timeout']),
            width=150,
            on_change=lambda e: self._update_config('timeout', int(e.control.value or 30))
        )
        
        # System prompt
        self.system_prompt_field = ft.TextField(
            label="System Prompt",
            value=self.ai_config['system_prompt'],
            multiline=True,
            min_lines=6,
            max_lines=10,
            width=600,
            on_change=lambda e: self._update_config('system_prompt', e.control.value)
        )
        
        # Provider-specific help
        provider_help = ft.Container(
            content=ft.Column([
                ft.Text("Provider Configuration Help:", weight=ft.FontWeight.BOLD),
                ft.Text("• OpenRouter: Single API for multiple models (RECOMMENDED)", size=12, color=ft.Colors.GREEN_700),
                ft.Text("  - Use one API key for GPT-4, Claude, Gemini, and more", size=11, color=ft.Colors.GREY_700),
                ft.Text("  - Models: gpt-4o, claude-3-5-sonnet, gemini-pro, llama-3.1", size=11, color=ft.Colors.GREY_700),
                ft.Text("• OpenAI: Direct access to GPT models", size=12),
                ft.Text("• Anthropic: Direct access to Claude models", size=12),
                ft.Text("• Google: Direct access to Gemini models", size=12),
                ft.Text("• Custom: Configure your own API endpoint", size=12),
                ft.Text("• Local: Use Ollama, LM Studio, or similar", size=12)
            ]),
            padding=ft.padding.all(15),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=8,
            width=600
        )
        
        content = ft.Column([
            ft.Text("AI Provider Configuration", size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            
            ft.Row([
                ft.Column([
                    self.provider_dropdown,
                    api_url_field,
                    self.api_key_field,
                    self.model_id_field
                ], spacing=15),
                ft.VerticalDivider(width=20),
                ft.Column([
                    ft.Text("Parameters:", weight=ft.FontWeight.BOLD),
                    max_tokens_field,
                    temperature_field,
                    timeout_field
                ], spacing=15)
            ]),
            
            ft.Divider(height=20),
            ft.Text("System Prompt:", weight=ft.FontWeight.BOLD),
            self.system_prompt_field,
            
            ft.Divider(height=20),
            provider_help
        ], spacing=10)
        
        return ft.Container(
            content=content,
            padding=ft.padding.all(20)
        )
    
    def _build_security_tab(self) -> ft.Container:
        """Build security and privacy configuration tab."""
        
        # Privacy settings
        self.privacy_mode_switch = ft.Switch(
            label="Privacy Mode (Anonymize Data)",
            value=self.security_config['enable_data_anonymization'],
            on_change=lambda e: self._update_security_config('enable_data_anonymization', e.control.value)
        )
        
        self.encryption_switch = ft.Switch(
            label="Enable Data Encryption",
            value=self.security_config['enable_encryption'],
            on_change=lambda e: self._update_security_config('enable_encryption', e.control.value)
        )
        
        self.audit_logging_switch = ft.Switch(
            label="Enable Audit Logging",
            value=self.security_config['enable_audit_logging'],
            on_change=lambda e: self._update_security_config('enable_audit_logging', e.control.value)
        )
        
        # Data retention policy
        self.data_retention_dropdown = ft.Dropdown(
            label="Data Retention Policy",
            value=self.security_config['data_retention_policy'],
            options=[
                ft.dropdown.Option("no_retention", "No Data Retention"),
                ft.dropdown.Option("session_only", "Session Only"),
                ft.dropdown.Option("24_hours", "24 Hours"),
                ft.dropdown.Option("7_days", "7 Days"),
                ft.dropdown.Option("30_days", "30 Days")
            ],
            width=250,
            on_change=lambda e: self._update_security_config('data_retention_policy', e.control.value)
        )
        
        # Access control
        access_control_switch = ft.Switch(
            label="Enable Access Control",
            value=self.security_config['access_control_enabled'],
            on_change=lambda e: self._update_security_config('access_control_enabled', e.control.value)
        )
        
        # Domain restrictions
        allowed_domains_field = ft.TextField(
            label="Allowed Domains (comma-separated)",
            value=",".join(self.security_config['allowed_domains']),
            width=400,
            on_change=lambda e: self._update_security_config('allowed_domains', [d.strip() for d in e.control.value.split(",")])
        )
        
        blocked_domains_field = ft.TextField(
            label="Blocked Domains (comma-separated)",
            value=",".join(self.security_config['blocked_domains']),
            width=400,
            on_change=lambda e: self._update_security_config('blocked_domains', [d.strip() for d in e.control.value.split(",")])
        )
        
        # Security recommendations
        security_recommendations = ft.Container(
            content=ft.Column([
                ft.Text("Security Recommendations:", weight=ft.FontWeight.BOLD),
                ft.Text("✓ Enable privacy mode for sensitive data", size=12, color=ft.Colors.GREEN_700),
                ft.Text("✓ Use encryption for data at rest", size=12, color=ft.Colors.GREEN_700),
                ft.Text("✓ Enable audit logging for compliance", size=12, color=ft.Colors.GREEN_700),
                ft.Text("✓ Set appropriate data retention policies", size=12, color=ft.Colors.GREEN_700),
                ft.Text("✓ Use access control for production environments", size=12, color=ft.Colors.GREEN_700)
            ]),
            padding=ft.padding.all(15),
            bgcolor=ft.Colors.GREEN_50,
            border_radius=8,
            width=600
        )
        
        content = ft.Column([
            ft.Text("Security & Privacy Settings", size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            
            ft.Row([
                ft.Column([
                    ft.Text("Privacy Settings:", weight=ft.FontWeight.BOLD),
                    self.privacy_mode_switch,
                    self.encryption_switch,
                    self.audit_logging_switch,
                    self.data_retention_dropdown
                ], spacing=15),
                ft.VerticalDivider(width=20),
                ft.Column([
                    ft.Text("Access Control:", weight=ft.FontWeight.BOLD),
                    access_control_switch,
                    allowed_domains_field,
                    blocked_domains_field
                ], spacing=15)
            ]),
            
            ft.Divider(height=20),
            security_recommendations
        ], spacing=10)
        
        return ft.Container(
            content=content,
            padding=ft.padding.all(20)
        )
    
    def _build_performance_tab(self) -> ft.Container:
        """Build performance configuration tab."""
        
        # Parallel processing
        parallel_processing_switch = ft.Switch(
            label="Enable Parallel Processing",
            value=self.performance_config['enable_parallel_processing'],
            on_change=lambda e: self._update_performance_config('enable_parallel_processing', e.control.value)
        )
        
        max_workers_field = ft.TextField(
            label="Max Workers",
            value=str(self.performance_config['max_workers']),
            width=150,
            on_change=lambda e: self._update_performance_config('max_workers', int(e.control.value or 4))
        )
        
        # Caching
        ai_cache_switch = ft.Switch(
            label="Enable AI Result Caching",
            value=self.performance_config['enable_ai_cache'],
            on_change=lambda e: self._update_performance_config('enable_ai_cache', e.control.value)
        )
        
        cache_ttl_field = ft.TextField(
            label="Cache TTL (minutes)",
            value=str(self.performance_config['cache_ttl_minutes']),
            width=150,
            on_change=lambda e: self._update_performance_config('cache_ttl_minutes', int(e.control.value or 60))
        )
        
        # Optimization
        batch_processing_switch = ft.Switch(
            label="Enable Batch Processing",
            value=self.performance_config['batch_processing'],
            on_change=lambda e: self._update_performance_config('batch_processing', e.control.value)
        )
        
        optimize_speed_switch = ft.Switch(
            label="Optimize for Speed",
            value=self.performance_config['optimize_for_speed'],
            on_change=lambda e: self._update_performance_config('optimize_for_speed', e.control.value)
        )
        
        # Performance monitoring
        performance_monitoring = ft.Container(
            content=ft.Column([
                ft.Text("Performance Monitoring:", weight=ft.FontWeight.BOLD),
                ft.Row([
                    ft.Column([
                        ft.Text("AI Processing:", size=12),
                        ft.Text("Average: 2.3s", size=12, color=ft.Colors.GREEN_700),
                        ft.Text("Last 24h: 1.8s", size=12, color=ft.Colors.GREEN_700)
                    ]),
                    ft.Column([
                        ft.Text("Report Generation:", size=12),
                        ft.Text("Average: 45s", size=12, color=ft.Colors.BLUE_700),
                        ft.Text("Last 24h: 38s", size=12, color=ft.Colors.BLUE_700)
                    ]),
                    ft.Column([
                        ft.Text("Export Operations:", size=12),
                        ft.Text("Average: 12s", size=12, color=ft.Colors.ORANGE_700),
                        ft.Text("Last 24h: 9s", size=12, color=ft.Colors.ORANGE_700)
                    ])
                ])
            ]),
            padding=ft.padding.all(15),
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            width=600
        )
        
        content = ft.Column([
            ft.Text("Performance Settings", size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            
            ft.Row([
                ft.Column([
                    ft.Text("Processing:", weight=ft.FontWeight.BOLD),
                    parallel_processing_switch,
                    max_workers_field,
                    ft.Divider(height=10),
                    ft.Text("Caching:", weight=ft.FontWeight.BOLD),
                    ai_cache_switch,
                    cache_ttl_field
                ], spacing=15),
                ft.VerticalDivider(width=20),
                ft.Column([
                    ft.Text("Optimization:", weight=ft.FontWeight.BOLD),
                    batch_processing_switch,
                    optimize_speed_switch,
                    ft.Divider(height=10),
                    ft.ElevatedButton(
                        "Clear AI Cache",
                        on_click=self._clear_ai_cache,
                        icon=ft.Icons.CLEAR_ALL
                    )
                ], spacing=15)
            ]),
            
            ft.Divider(height=20),
            performance_monitoring
        ], spacing=10)
        
        return ft.Container(
            content=content,
            padding=ft.padding.all(20)
        )
    
    def _build_advanced_tab(self) -> ft.Container:
        """Build advanced configuration tab."""
        
        # Rate limiting
        rate_limit_field = ft.TextField(
            label="Rate Limit (requests/minute)",
            value=str(self.ai_config['rate_limit']),
            width=200,
            on_change=lambda e: self._update_config('rate_limit', int(e.control.value or 10))
        )
        
        # Custom prompts
        custom_prompts_section = ft.Container(
            content=ft.Column([
                ft.Text("Custom Prompts:", weight=ft.FontWeight.BOLD),
                ft.Row([
                    ft.ElevatedButton(
                        "Financial Analysis",
                        on_click=lambda _: self._edit_custom_prompt("financial_analysis"),
                        icon=ft.Icons.ANALYTICS
                    ),
                    ft.ElevatedButton(
                        "Risk Assessment",
                        on_click=lambda _: self._edit_custom_prompt("risk_assessment"),
                        icon=ft.Icons.WARNING
                    ),
                    ft.ElevatedButton(
                        "Chart Analysis",
                        on_click=lambda _: self._edit_custom_prompt("chart_analysis"),
                        icon=ft.Icons.BAR_CHART
                    )
                ])
            ]),
            padding=ft.padding.all(15),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=8,
            width=600
        )
        
        # Model management
        model_management = ft.Container(
            content=ft.Column([
                ft.Text("Model Management:", weight=ft.FontWeight.BOLD),
                ft.Row([
                    ft.ElevatedButton(
                        "Download Model",
                        on_click=self._download_model,
                        icon=ft.Icons.DOWNLOAD
                    ),
                    ft.ElevatedButton(
                        "Update Model",
                        on_click=self._update_model,
                        icon=ft.Icons.UPDATE
                    ),
                    ft.ElevatedButton(
                        "Model Info",
                        on_click=self._show_model_info,
                        icon=ft.Icons.INFO
                    )
                ])
            ]),
            padding=ft.padding.all(15),
            bgcolor=ft.Colors.GREEN_50,
            border_radius=8,
            width=600
        )
        
        # Debug settings
        debug_settings = ft.Container(
            content=ft.Column([
                ft.Text("Debug Settings:", weight=ft.FontWeight.BOLD),
                ft.Switch(
                    label="Enable Debug Logging",
                    value=False,
                    on_change=lambda e: self._toggle_debug_logging(e.control.value)
                ),
                ft.Switch(
                    label="Save AI Interactions",
                    value=False,
                    on_change=lambda e: self._toggle_ai_interaction_logging(e.control.value)
                ),
                ft.ElevatedButton(
                    "View Debug Logs",
                    on_click=self._view_debug_logs,
                    icon=ft.Icons.BUG_REPORT
                )
            ]),
            padding=ft.padding.all(15),
            bgcolor=ft.Colors.ORANGE_50,
            border_radius=8,
            width=600
        )
        
        content = ft.Column([
            ft.Text("Advanced Settings", size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            
            ft.Row([
                ft.Column([
                    ft.Text("Rate Limiting:", weight=ft.FontWeight.BOLD),
                    rate_limit_field
                ], spacing=15)
            ]),
            
            ft.Divider(height=20),
            custom_prompts_section,
            
            ft.Divider(height=20),
            model_management,
            
            ft.Divider(height=20),
            debug_settings
        ], spacing=10)
        
        return ft.Container(
            content=content,
            padding=ft.padding.all(20)
        )
    
    def _get_default_system_prompt(self) -> str:
        """Get default system prompt for AI analysis."""
        return """You are a professional financial analyst specializing in renewable energy projects.
Analyze the provided financial data and generate comprehensive insights.

Your analysis should include:
1. Executive summary of financial performance
2. Key strengths and weaknesses
3. Risk assessment with specific recommendations
4. Market positioning analysis
5. Investment recommendation with rationale
6. Strategic next steps

Format your response as structured content with clear sections.
Be precise, professional, and focus on actionable insights.
Avoid speculation and base all conclusions on the provided data.
Use industry-standard financial terminology and metrics."""
    
    def _on_provider_change(self, e):
        """Handle provider change."""
        provider = e.control.value
        self.ai_config['provider'] = provider
        
        # Update default settings based on provider
        if provider == 'openrouter':
            self.ai_config['api_url'] = 'https://openrouter.ai/api/v1/chat/completions'
            self.ai_config['model_id'] = 'moonshotai/kimi-k2:free'
        elif provider == 'openai':
            self.ai_config['api_url'] = 'https://api.openai.com/v1/chat/completions'
            self.ai_config['model_id'] = 'gpt-4o'
        elif provider == 'anthropic':
            self.ai_config['api_url'] = 'https://api.anthropic.com/v1/messages'
            self.ai_config['model_id'] = 'claude-3-5-sonnet-20241022'
        elif provider == 'google':
            self.ai_config['api_url'] = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent'
            self.ai_config['model_id'] = 'gemini-pro'
        elif provider == 'local':
            self.ai_config['api_url'] = 'http://localhost:11434/api/generate'
            self.ai_config['model_id'] = 'llama3.1'
        
        # Update UI fields
        self.page.update()
    
    def _update_config(self, key: str, value: Any):
        """Update AI configuration."""
        self.ai_config[key] = value
    
    def _update_security_config(self, key: str, value: Any):
        """Update security configuration."""
        self.security_config[key] = value
    
    def _update_performance_config(self, key: str, value: Any):
        """Update performance configuration."""
        self.performance_config[key] = value
    
    async def _test_ai_connection(self, e):
        """Test AI connection with real API call."""
        if not self.ai_config['api_key'] and self.ai_config['provider'] != 'local':
            self.show_error("API key is required for this provider")
            return
        
        self.connection_status.visible = True
        self.connection_status.content = ft.Row([
            ft.ProgressRing(width=20, height=20),
            ft.Text("Testing connection...", color=ft.Colors.BLUE_600)
        ])
        self.page.update()
        
        try:
            import requests
            
            # Test message
            test_message = {
                "model": self.ai_config['model_id'],
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello! This is a connection test. Please respond with 'Connection successful'."
                    }
                ],
                "max_tokens": 50,
                "temperature": 0.3
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.ai_config['api_key']}"
            }
            
            # Provider-specific headers
            if self.ai_config['provider'] == 'openrouter':
                headers["HTTP-Referer"] = "https://github.com/your-app"
                headers["X-Title"] = "Hiel Financial Model AI"
            
            # Make API call
            response = requests.post(
                self.ai_config['api_url'],
                json=test_message,
                headers=headers,
                timeout=self.ai_config['timeout']
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # Extract response content
                if 'choices' in result and len(result['choices']) > 0:
                    ai_response = result['choices'][0].get('message', {}).get('content', '')
                    
                    self.connection_status.content = ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN_600),
                            ft.Text("Connection successful!", color=ft.Colors.GREEN_600)
                        ]),
                        ft.Text(f"Model: {self.ai_config['model_id']}", size=11, color=ft.Colors.GREEN_700),
                        ft.Text(f"Response: {ai_response[:50]}...", size=11, color=ft.Colors.GREEN_700)
                    ])
                    self.connection_status.bgcolor = ft.Colors.GREEN_50
                    self.connection_tested = True
                    self.connection_result = "success"
                else:
                    raise Exception("Invalid response format")
                    
            else:
                error_msg = f"HTTP {response.status_code}: {response.text[:100]}"
                self.connection_status.content = ft.Row([
                    ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED_600),
                    ft.Text(f"Connection failed: {error_msg}", color=ft.Colors.RED_600)
                ])
                self.connection_status.bgcolor = ft.Colors.RED_50
                self.connection_tested = True
                self.connection_result = "failed"
        
        except requests.exceptions.Timeout:
            self.connection_status.content = ft.Row([
                ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED_600),
                ft.Text("Connection timeout - check your internet connection", color=ft.Colors.RED_600)
            ])
            self.connection_status.bgcolor = ft.Colors.RED_50
            self.connection_tested = True
            self.connection_result = "timeout"
            
        except requests.exceptions.ConnectionError:
            self.connection_status.content = ft.Row([
                ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED_600),
                ft.Text("Connection error - check API URL and internet", color=ft.Colors.RED_600)
            ])
            self.connection_status.bgcolor = ft.Colors.RED_50
            self.connection_tested = True
            self.connection_result = "connection_error"
            
        except Exception as ex:
            self.connection_status.content = ft.Row([
                ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED_600),
                ft.Text(f"Error: {str(ex)[:100]}...", color=ft.Colors.RED_600)
            ])
            self.connection_status.bgcolor = ft.Colors.RED_50
            self.connection_tested = True
            self.connection_result = "error"
        
        self.page.update()
    
    def _save_settings(self, e):
        """Save settings to file."""
        try:
            settings = {
                'ai_config': self.ai_config,
                'security_config': self.security_config,
                'performance_config': self.performance_config,
                'saved_at': datetime.now().isoformat()
            }
            
            settings_path = Path('config/ai_settings.json')
            settings_path.parent.mkdir(exist_ok=True)
            
            with open(settings_path, 'w') as f:
                json.dump(settings, f, indent=2)
            
            self.show_success("Settings saved successfully")
            
            # Notify parent about settings change
            self.request_action("ai_settings_updated", settings)
            
        except Exception as ex:
            self.show_error(f"Failed to save settings: {str(ex)}")
    
    def _load_settings(self):
        """Load settings from file."""
        try:
            settings_path = Path('config/ai_settings.json')
            if settings_path.exists():
                with open(settings_path, 'r') as f:
                    settings = json.load(f)
                
                self.ai_config.update(settings.get('ai_config', {}))
                self.security_config.update(settings.get('security_config', {}))
                self.performance_config.update(settings.get('performance_config', {}))
                
        except Exception as ex:
            self.logger.error(f"Failed to load settings: {str(ex)}")
    
    def _reset_to_defaults(self, e):
        """Reset settings to defaults."""
        def confirm_reset(e):
            # Reset configurations
            self.ai_config = {
                'provider': 'openrouter',
                'api_url': 'https://openrouter.ai/api/v1/chat/completions',
                'api_key': '',
                'model_id': 'moonshotai/kimi-k2:free',
                'system_prompt': self._get_default_system_prompt(),
                'max_tokens': 4000,
                'temperature': 0.3,
                'timeout': 60,
                'rate_limit': 10,
                'privacy_mode': True,
                'enable_caching': True,
                'enable_streaming': False,
                'custom_headers': {}
            }
            
            self.security_config = {
                'enable_encryption': True,
                'enable_data_anonymization': True,
                'enable_audit_logging': True,
                'data_retention_policy': 'no_retention',
                'access_control_enabled': False,
                'allowed_domains': [],
                'blocked_domains': []
            }
            
            self.performance_config = {
                'enable_parallel_processing': True,
                'max_workers': 4,
                'enable_ai_cache': True,
                'cache_ttl_minutes': 60,
                'batch_processing': False,
                'optimize_for_speed': True
            }
            
            # Update UI
            self.refresh()
            self.show_success("Settings reset to defaults")
            dialog.open = False
            self.page.update()
        
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Reset Settings"),
            content=ft.Text("Are you sure you want to reset all settings to defaults? This action cannot be undone."),
            actions=[
                ft.TextButton("Cancel", on_click=lambda _: setattr(dialog, 'open', False)),
                ft.TextButton("Reset", on_click=confirm_reset)
            ]
        )
        
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()
    
    def _export_config(self, e):
        """Export configuration to file."""
        try:
            config_data = {
                'ai_config': self.ai_config,
                'security_config': self.security_config,
                'performance_config': self.performance_config,
                'exported_at': datetime.now().isoformat(),
                'version': '2.0'
            }
            
            # Remove sensitive information
            config_data['ai_config'] = {k: v for k, v in config_data['ai_config'].items() if k != 'api_key'}
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_path = Path(f'exports/ai_config_{timestamp}.json')
            export_path.parent.mkdir(exist_ok=True)
            
            with open(export_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            self.show_success(f"Configuration exported to {export_path}")
            
        except Exception as ex:
            self.show_error(f"Failed to export configuration: {str(ex)}")
    
    def _edit_custom_prompt(self, prompt_type: str):
        """Edit custom prompt."""
        # This would open a dialog to edit the specific prompt
        self.show_info(f"Edit {prompt_type} prompt dialog would open here")
    
    def _download_model(self, e):
        """Download AI model."""
        self.show_info("Model download functionality would be implemented here")
    
    def _update_model(self, e):
        """Update AI model."""
        self.show_info("Model update functionality would be implemented here")
    
    def _show_model_info(self, e):
        """Show model information."""
        info_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Model Information"),
            content=ft.Column([
                ft.Text(f"Provider: {self.ai_config['provider']}"),
                ft.Text(f"Model: {self.ai_config['model_id']}"),
                ft.Text(f"Max Tokens: {self.ai_config['max_tokens']}"),
                ft.Text(f"Temperature: {self.ai_config['temperature']}"),
                ft.Text(f"Timeout: {self.ai_config['timeout']}s"),
                ft.Text(f"Rate Limit: {self.ai_config['rate_limit']}/min")
            ]),
            actions=[
                ft.TextButton("Close", on_click=lambda _: setattr(info_dialog, 'open', False))
            ]
        )
        
        self.page.dialog = info_dialog
        info_dialog.open = True
        self.page.update()
    
    def _clear_ai_cache(self, e):
        """Clear AI cache."""
        self.show_success("AI cache cleared")
        self.request_action("clear_ai_cache", {})
    
    def _toggle_debug_logging(self, enabled: bool):
        """Toggle debug logging."""
        self.show_info(f"Debug logging {'enabled' if enabled else 'disabled'}")
    
    def _toggle_ai_interaction_logging(self, enabled: bool):
        """Toggle AI interaction logging."""
        self.show_info(f"AI interaction logging {'enabled' if enabled else 'disabled'}")
    
    def _view_debug_logs(self, e):
        """View debug logs."""
        self.show_info("Debug logs viewer would open here")
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get current AI configuration."""
        return self.ai_config.copy()
    
    def get_security_config(self) -> Dict[str, Any]:
        """Get current security configuration."""
        return self.security_config.copy()
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get current performance configuration."""
        return self.performance_config.copy()
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with new data."""
        if "ai_status" in data:
            # Update connection status based on AI service status
            status = data["ai_status"]
            if status.get("status") == "active":
                self.connection_status.content = ft.Row([
                    ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN_600),
                    ft.Text("AI service active", color=ft.Colors.GREEN_600)
                ])
                self.connection_status.bgcolor = ft.Colors.GREEN_50
                self.connection_status.visible = True
                self.page.update()
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate current configuration."""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Validate AI configuration
        if not self.ai_config['api_key'] and self.ai_config['provider'] != 'local':
            validation_result['errors'].append("API key is required for this provider")
            validation_result['valid'] = False
        
        if not self.ai_config['model_id']:
            validation_result['errors'].append("Model ID is required")
            validation_result['valid'] = False
        
        if self.ai_config['max_tokens'] <= 0:
            validation_result['errors'].append("Max tokens must be positive")
            validation_result['valid'] = False
        
        if not (0 <= self.ai_config['temperature'] <= 2):
            validation_result['warnings'].append("Temperature should be between 0 and 2")
        
        # Validate security configuration
        if self.security_config['enable_encryption'] and not self.security_config['enable_data_anonymization']:
            validation_result['warnings'].append("Consider enabling data anonymization with encryption")
        
        # Validate performance configuration
        if self.performance_config['max_workers'] > 8:
            validation_result['warnings'].append("High worker count may impact system performance")
        
        return validation_result